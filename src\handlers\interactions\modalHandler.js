import { logger } from '../../utils/logging/logger.js';
import ShoppingCartHandler from '../cart/shoppingCartHandler.js';
import CartModalHandler from '../cart/cartModalHandler.js';
import { StoreModalHandler } from '../modals/storeModalHandler.js';
import { AdminModalHandler } from '../modals/adminModalHandler.js';
import { ConfigModalHandler } from '../modals/configModalHandler.js';
import { ReviewModalHandler } from '../modals/reviewModalHandler.js';

/**
 * Manipula interações de modais
 * @param {ModalSubmitInteraction} interaction
 */
export async function handleModal(interaction) {
    const customId = interaction.customId;

    try {
        logger.info(`Modal enviado: ${customId} por ${interaction.user.tag}`);

        // Roteamento baseado no customId do modal
        // Para modais de avaliação, o customId tem formato: review_modal_cartId
        if (customId.startsWith('review_modal_')) {
            await ReviewModalHandler.handleReviewModal(interaction);
            return;
        }

        // Para modais de edição, o customId tem formato: store_edit_storeId
        if (customId.startsWith('store_edit_')) {
            const storeId = customId.replace('store_edit_', '');
            await StoreModalHandler.handleStoreEditModal(interaction, storeId);
            return;
        }

        // Para modais de criação de produto, o customId tem formato: product_create_storeId
        if (customId.startsWith('product_create_')) {
            const storeId = customId.replace('product_create_', '');
            await StoreModalHandler.handleProductCreateModal(interaction, storeId);
            return;
        }

        // Para modais de criação de estoque, o customId tem formato: stock_create_storeId_productId
        if (customId.startsWith('stock_create_')) {
            const parts = customId.replace('stock_create_', '').split('_');
            const storeId = parts[0];
            const productId = parts[1];
            await StoreModalHandler.handleStockCreateModal(interaction, storeId, productId);
            return;
        }

        // Para modais de edição de item de estoque, o customId tem formato: edit_stock_item_modal_stockItemId
        if (customId.startsWith('edit_stock_item_modal_')) {
            const stockItemId = customId.replace('edit_stock_item_modal_', '');
            await StoreModalHandler.handleEditStockItemModal(interaction, stockItemId);
            return;
        }

        // Para modais de edição de produto, o customId tem formato: edit_product_basic_modal_productId ou edit_product_price_modal_productId
        if (customId.startsWith('edit_product_') && customId.includes('_modal_')) {
            await AdminModalHandler.handleEditProductModal(interaction);
            return;
        }

        // Para modais de busca no estoque, o customId tem formato: stock_search_modal_productId
        if (customId.startsWith('stock_search_modal_')) {
            const productId = customId.replace('stock_search_modal_', '');
            await StoreModalHandler.handleStockSearchModal(interaction, productId);
            return;
        }

        // Para modais de quantidade do carrinho, o customId tem formato: cart_quantity_modal_productId
        if (customId.startsWith('cart_quantity_modal_')) {
            await ShoppingCartHandler.handleQuantityModal(interaction);
            return;
        }

        // Para modal de gerenciamento do carrinho fixo
        if (customId === 'cart_manage_modal') {
            await CartModalHandler.handleCartManageModal(interaction);
            return;
        }

        const [category, action] = customId.split('_');

        switch (category) {
            case 'store':
                await StoreModalHandler.handleStoreModal(interaction, action);
                break;
            case 'admin':
                await AdminModalHandler.handleAdminModal(interaction, action);
                break;
            case 'config':
                await ConfigModalHandler.handleConfigModal(interaction);
                break;
            default:
                logger.warn(`Categoria de modal não reconhecida: ${category}`);
                await interaction.reply({
                    content: '❌ Modal não reconhecido.',
                    ephemeral: true
                });
        }

    } catch (error) {
        logger.error(`Erro ao processar modal ${customId}:`, error);

        const errorMessage = {
            content: '❌ Erro ao processar o formulário.',
            ephemeral: true
        };

        try {
            if (interaction.replied) {
                await interaction.followUp(errorMessage);
            } else if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        } catch (replyError) {
            logger.error(`Erro ao enviar resposta de erro para modal ${customId}:`, replyError);
        }
    }
}