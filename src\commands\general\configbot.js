import { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { COLORS, EMOJIS } from '../../config/constants.js';
import BotConfig from '../../models/BotConfig.js';
import { logger } from '../../utils/logging/logger.js';

export default {
    data: new SlashCommandBuilder()
        .setName('configbot')
        .setDescription('Configura as opções do bot (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Log da execução do comando
            await logger.userAction('Comando configbot executado', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'configbot'
            }, {
                user: interaction.user.tag,
                guild: interaction.guild.name
            });

            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                await logger.security('Tentativa de uso de comando admin por usuário não autorizado', {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id,
                    command: 'configbot'
                });

                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            // Busca configuração atual do servidor
            const config = await BotConfig.findByGuild(interaction.guild.id);
            
            // Conta canais de log configurados
            const logChannels = config?.logChannels || {};
            const configuredChannels = Object.values(logChannels).filter(Boolean).length;
            const totalChannels = 10; // Total de tipos de log disponíveis

            // Cria embed com configurações atuais
            const embed = new EmbedBuilder()
                .setColor(COLORS.PRIMARY)
                .setTitle(`${EMOJIS.INFO} Configurações do Bot`)
                .setDescription('Configure as opções do bot para este servidor')
                .setThumbnail(interaction.client.user.displayAvatarURL())
                .addFields(
                    {
                        name: '📊 Sistema de Logs',
                        value: `**${configuredChannels}/${totalChannels}** canais configurados\n` +
                               `Nível: ${config?.logSettings?.discordLogLevel || 'WARN'}`,
                        inline: true
                    },
                    {
                        name: '🔧 Canais Principais',
                        value: `**Admin:** ${config?.logChannels?.admin ? `<#${config.logChannels.admin}>` : '`Não configurado`'}\n` +
                               `**Público:** ${config?.logChannels?.public ? `<#${config.logChannels.public}>` : '`Não configurado`'}\n` +
                               `**Erro:** ${config?.logChannels?.error ? `<#${config.logChannels.error}>` : '`Não configurado`'}`,
                        inline: true
                    },
                    {
                        name: '⚙️ Canais Sistema',
                        value: `**Sistema:** ${config?.logChannels?.system ? `<#${config.logChannels.system}>` : '`Não configurado`'}\n` +
                               `**Comandos:** ${config?.logChannels?.commands ? `<#${config.logChannels.commands}>` : '`Não configurado`'}\n` +
                               `**Eventos:** ${config?.logChannels?.events ? `<#${config.logChannels.events}>` : '`Não configurado`'}`,
                        inline: true
                    },
                    {
                        name: '💳 MercadoPago',
                        value: config?.mercadoPago?.isEnabled
                            ? `${EMOJIS.SUCCESS} Configurado`
                            : '`Não configurado`',
                        inline: true
                    },
                    {
                        name: '⚡ Rate Limiting',
                        value: config?.rateLimiting?.isEnabled !== false
                            ? `${EMOJIS.SUCCESS} Ativo (${config?.rateLimiting?.maxRequests || 10} req/min)`
                            : '`Desativado`',
                        inline: true
                    },
                    {
                        name: '⭐ Chat Avaliações',
                        value: config?.reviewsChannelId
                            ? `<#${config.reviewsChannelId}>`
                            : '`Não configurado`',
                        inline: true
                    },
                    {
                        name: '🕐 Última Modificação',
                        value: config?.updatedAt
                            ? `<t:${Math.floor(config.updatedAt.getTime() / 1000)}:R>`
                            : '`Nunca`',
                        inline: true
                    }
                )
                .setFooter({ 
                    text: `Servidor: ${interaction.guild.name}`,
                    iconURL: interaction.guild.iconURL()
                })
                .setTimestamp();

            // Cria botões de configuração
            const row1 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('config_log_channels')
                        .setLabel('Canais de Log')
                        .setEmoji('📋')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('config_log_settings')
                        .setLabel('Config. Logs')
                        .setEmoji('⚙️')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('config_mercadopago')
                        .setLabel('MercadoPago')
                        .setEmoji('💳')
                        .setStyle(ButtonStyle.Success)
                );

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('config_rate_limit')
                        .setLabel('Rate Limiting')
                        .setEmoji('⚡')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('config_reviews_channel')
                        .setLabel('Chat Avaliações')
                        .setEmoji('⭐')
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('config_test_logs')
                        .setLabel('Testar Logs')
                        .setEmoji('🧪')
                        .setStyle(ButtonStyle.Secondary)
                );

            const row3 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('config_test_mercadopago')
                        .setLabel('Testar MercadoPago')
                        .setEmoji('🔍')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(!config?.mercadoPago?.isEnabled)
                );

            const row4 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('config_reset_all')
                        .setLabel('Resetar Tudo')
                        .setEmoji('🔄')
                        .setStyle(ButtonStyle.Danger)
                );

            await interaction.reply({
                embeds: [embed],
                components: [row1, row2, row3, row4],
                ephemeral: true
            });

        } catch (error) {
            await logger.logStructured('ERROR', 'COMMAND', 'Erro no comando configbot', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'configbot'
            }, {
                error: error.message,
                stack: error.stack,
                user: interaction.user.tag
            });

            const errorEmbed = new EmbedBuilder()
                .setColor(COLORS.ERROR)
                .setTitle(`${EMOJIS.ERROR} Erro`)
                .setDescription('Ocorreu um erro ao carregar as configurações do bot.')
                .setTimestamp();

            try {
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
                } else {
                    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            } catch (replyError) {
                await logger.logStructured('ERROR', 'COMMAND', 'Erro ao responder após falha no configbot', {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id
                }, {
                    originalError: error.message,
                    replyError: replyError.message
                });
            }
        }
    }
};