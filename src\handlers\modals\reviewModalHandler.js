import { logger } from '../../utils/logging/logger.js';
import { ReviewManager } from '../../utils/managers/reviewManager.js';
import ShoppingCart from '../../models/ShoppingCart.js';

/**
 * Handler para modais relacionados às avaliações
 */
export class ReviewModalHandler {
    /**
     * Manipula modais relacionados às avaliações
     * @param {Object} interaction - Interação do Discord
     */
    static async handleReviewModal(interaction) {
        try {
            const customId = interaction.customId;
            logger.info(`Modal de avaliação enviado: ${customId} por ${interaction.user.tag}`);

            if (customId.startsWith('review_modal_')) {
                await this.handleReviewSubmission(interaction);
            } else {
                logger.warn(`Modal de avaliação não reconhecido: ${customId}`);
                await interaction.reply({
                    content: '❌ Modal não reconhecido.',
                    ephemeral: true
                });
            }

        } catch (error) {
            await logger.error('Erro no handler de modal de avaliação:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Erro ao processar avaliação. Tente novamente.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula envio de avaliação via modal
     * @param {Object} interaction - Interação do Discord
     */
    static async handleReviewSubmission(interaction) {
        try {
            // Extrai ID do carrinho do customId
            const cartId = interaction.customId.replace('review_modal_', '');
            
            // Busca o carrinho
            const cart = await ShoppingCart.findById(cartId);
            if (!cart) {
                return await interaction.reply({
                    content: '❌ Carrinho não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.reply({
                    content: '❌ Você não pode avaliar este pedido.',
                    ephemeral: true
                });
            }

            // Processa a avaliação
            await ReviewManager.processReview(interaction, cart);

            // Desabilita botões da mensagem original de avaliação
            await this.disableOriginalReviewMessage(interaction, cartId);

            await logger.info(`Avaliação processada com sucesso para carrinho ${cartId}`);

        } catch (error) {
            await logger.error('Erro ao processar envio de avaliação:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Erro ao processar sua avaliação. Tente novamente.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Desabilita botões da mensagem original de avaliação
     * @param {Object} interaction - Interação do Discord
     * @param {string} cartId - ID do carrinho
     */
    static async disableOriginalReviewMessage(interaction, cartId) {
        try {
            const { ActionRowBuilder, ButtonBuilder, ButtonStyle, EmbedBuilder } = await import('discord.js');
            const { COLORS } = await import('../../config/constants.js');
            const { getEmoji } = await import('../../utils/ui/emojiHelper.js');

            // Busca a mensagem original de avaliação no canal
            const messages = await interaction.channel.messages.fetch({ limit: 50 });
            const reviewMessage = messages.find(msg => 
                msg.embeds.length > 0 && 
                msg.embeds[0].title?.includes('Avalie sua Compra') &&
                msg.components.length > 0 &&
                msg.components[0].components.some(component => 
                    component.customId === `review_send_${cartId}` || 
                    component.customId === `review_skip_${cartId}`
                )
            );

            if (reviewMessage) {
                // Cria botões desabilitados
                const disabledRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('review_send_disabled')
                            .setLabel('Enviar Avaliação')
                            .setEmoji('⭐')
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(true),
                        new ButtonBuilder()
                            .setCustomId('review_skip_disabled')
                            .setLabel('Não Avaliar')
                            .setEmoji('❌')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(true)
                    );

                // Cria embed de avaliação enviada
                const successEmoji = await getEmoji(interaction.guild.id, 'SUCCESS') || '✅';
                const starEmoji = await getEmoji(interaction.guild.id, 'STAR') || '⭐';
                
                const embed = new EmbedBuilder()
                    .setColor(COLORS.SUCCESS)
                    .setTitle(`${successEmoji} Avaliação Enviada`)
                    .setDescription(
                        `${starEmoji} **Obrigado pela sua avaliação!**\n\n` +
                        'Sua opinião foi enviada com sucesso e ajudará outros clientes.\n' +
                        'Sua compra foi finalizada!'
                    )
                    .setFooter({
                        text: 'Sistema de Avaliações - Concluído',
                        iconURL: interaction.client.user.displayAvatarURL()
                    })
                    .setTimestamp();

                // Atualiza mensagem original
                await reviewMessage.edit({
                    embeds: [embed],
                    components: [disabledRow]
                });
            }

        } catch (error) {
            await logger.error('Erro ao desabilitar mensagem original de avaliação:', error);
            // Não falha o processo por causa deste erro
        }
    }
}

export default ReviewModalHandler;
