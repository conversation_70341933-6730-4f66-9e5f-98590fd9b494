import { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } from 'discord.js';
import { COLORS, EMOJIS, BOT_CONFIG } from '../../config/constants.js';
import BotConfig from '../../models/BotConfig.js';
import { logger } from '../logging/logger.js';
import { getEmoji } from '../ui/emojiHelper.js';

/**
 * Gerenciador do sistema de avaliações pós-compra
 */
export class ReviewManager {
    /**
     * Apresenta opções de avaliação após entrega dos produtos
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {Array} deliveredProducts - Produtos entregues
     */
    static async presentReviewOptions(channel, cart, deliveredProducts) {
        try {
            const starEmoji = await getEmoji(channel.guild.id, 'STAR') || '⭐';
            const heartEmoji = await getEmoji(channel.guild.id, 'HEART') || '❤️';
            const timeEmoji = await getEmoji(channel.guild.id, 'TIME') || '⏰';

            // Cria embed de solicitação de avaliação
            const embed = new EmbedBuilder()
                .setColor(COLORS.PRIMARY)
                .setTitle(`${starEmoji} Avalie sua Compra`)
                .setDescription(
                    `${heartEmoji} **Obrigado pela sua compra!**\n\n` +
                    `Sua opinião é muito importante para nós. Gostaria de avaliar os produtos que você acabou de receber?\n\n` +
                    `**Produtos entregues:**\n` +
                    deliveredProducts.map(product => 
                        `${product.emoji} **${product.name}** (${product.quantity}x)`
                    ).join('\n') + '\n\n' +
                    `${timeEmoji} **Você tem 5 minutos para decidir.**\n` +
                    `Após esse tempo, a compra será finalizada automaticamente.`
                )
                .setFooter({
                    text: `Pedido: ${cart.orderNumber || cart._id} | Sistema de Avaliações`,
                    iconURL: channel.client.user.displayAvatarURL()
                })
                .setTimestamp();

            // Cria botões de ação
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`review_send_${cart._id}`)
                        .setLabel('Enviar Avaliação')
                        .setEmoji('⭐')
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(`review_skip_${cart._id}`)
                        .setLabel('Não Avaliar')
                        .setEmoji('❌')
                        .setStyle(ButtonStyle.Secondary)
                );

            // Envia mensagem com opções de avaliação
            const reviewMessage = await channel.send({
                embeds: [embed],
                components: [row]
            });

            // Configura timeout de 5 minutos
            setTimeout(async () => {
                try {
                    await this.handleReviewTimeout(channel, cart, reviewMessage);
                } catch (error) {
                    await logger.error('Erro ao processar timeout de avaliação:', error);
                }
            }, 5 * 60 * 1000); // 5 minutos

            await logger.info(`Opções de avaliação apresentadas para carrinho ${cart._id}`);

        } catch (error) {
            await logger.error('Erro ao apresentar opções de avaliação:', error);
            // Não falha a entrega por causa da avaliação
            await this.finalizeWithoutReview(channel, cart, 'Erro no sistema de avaliações');
        }
    }

    /**
     * Processa timeout da avaliação
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {Object} reviewMessage - Mensagem de avaliação
     */
    static async handleReviewTimeout(channel, cart, reviewMessage) {
        try {
            // Desabilita botões da mensagem
            const disabledRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('review_send_disabled')
                        .setLabel('Enviar Avaliação')
                        .setEmoji('⭐')
                        .setStyle(ButtonStyle.Primary)
                        .setDisabled(true),
                    new ButtonBuilder()
                        .setCustomId('review_skip_disabled')
                        .setLabel('Não Avaliar')
                        .setEmoji('❌')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(true)
                );

            const timeoutEmoji = await getEmoji(channel.guild.id, 'TIME') || '⏰';
            const embed = new EmbedBuilder()
                .setColor(COLORS.WARNING)
                .setTitle(`${timeoutEmoji} Tempo Esgotado`)
                .setDescription(
                    'O tempo para enviar uma avaliação expirou.\n' +
                    'Sua compra foi finalizada com sucesso!'
                )
                .setFooter({
                    text: 'Sistema de Avaliações - Timeout',
                    iconURL: channel.client.user.displayAvatarURL()
                })
                .setTimestamp();

            // Atualiza mensagem original
            await reviewMessage.edit({
                embeds: [embed],
                components: [disabledRow]
            });

            // Finaliza compra sem avaliação
            await this.finalizeWithoutReview(channel, cart, 'Timeout de avaliação');

        } catch (error) {
            await logger.error('Erro ao processar timeout de avaliação:', error);
        }
    }

    /**
     * Finaliza compra sem avaliação
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {string} reason - Motivo da finalização sem avaliação
     */
    static async finalizeWithoutReview(channel, cart, reason) {
        try {
            // Registra no log que a compra foi finalizada sem avaliação
            await logger.userAction('Compra finalizada sem avaliação', {
                guildId: channel.guild.id,
                userId: cart.userId,
                command: 'purchase_complete'
            }, {
                cartId: cart._id.toString(),
                orderNumber: cart.orderNumber || cart._id,
                reason: reason,
                total: cart.subtotal,
                itemCount: cart.items.length
            });

            // Agenda limpeza do canal em 2 minutos
            setTimeout(async () => {
                try {
                    const ShoppingChannelManager = (await import('./shoppingChannelManager.js')).default;
                    await ShoppingChannelManager.cleanupShoppingChannel(
                        channel,
                        'Compra finalizada - sem avaliação'
                    );
                } catch (error) {
                    await logger.error('Erro ao limpar canal após finalização sem avaliação:', error);
                }
            }, 2 * 60 * 1000); // 2 minutos

        } catch (error) {
            await logger.error('Erro ao finalizar compra sem avaliação:', error);
        }
    }

    /**
     * Cria modal de avaliação
     * @param {string} cartId - ID do carrinho
     * @returns {ModalBuilder} Modal de avaliação
     */
    static createReviewModal(cartId) {
        const modal = new ModalBuilder()
            .setCustomId(`review_modal_${cartId}`)
            .setTitle('Avalie sua Compra');

        const ratingInput = new TextInputBuilder()
            .setCustomId('review_rating')
            .setLabel('Nota (1-5 estrelas)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Digite um número de 1 a 5')
            .setMinLength(1)
            .setMaxLength(1)
            .setRequired(true);

        const commentInput = new TextInputBuilder()
            .setCustomId('review_comment')
            .setLabel('Comentário (opcional)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Conte-nos sobre sua experiência com os produtos...')
            .setMinLength(0)
            .setMaxLength(1000)
            .setRequired(false);

        const row1 = new ActionRowBuilder().addComponents(ratingInput);
        const row2 = new ActionRowBuilder().addComponents(commentInput);

        modal.addComponents(row1, row2);
        return modal;
    }

    /**
     * Processa avaliação enviada
     * @param {Object} interaction - Interação do modal
     * @param {Object} cart - Carrinho de compras
     */
    static async processReview(interaction, cart) {
        try {
            const rating = parseInt(interaction.fields.getTextInputValue('review_rating'));
            const comment = interaction.fields.getTextInputValue('review_comment') || '';

            // Valida nota
            if (isNaN(rating) || rating < 1 || rating > 5) {
                return await interaction.reply({
                    content: '❌ Por favor, digite uma nota válida entre 1 e 5.',
                    ephemeral: true
                });
            }

            // Busca canal de avaliações configurado
            const config = await BotConfig.findByGuild(interaction.guild.id);
            if (!config?.reviewsChannelId) {
                await logger.warn('Canal de avaliações não configurado', {
                    guildId: interaction.guild.id,
                    cartId: cart._id.toString()
                });
                
                return await interaction.reply({
                    content: '⚠️ Canal de avaliações não configurado. Sua avaliação foi registrada nos logs.',
                    ephemeral: true
                });
            }

            // Envia avaliação para o canal configurado
            await this.sendReviewToChannel(interaction, cart, rating, comment, config.reviewsChannelId);

            // Confirma envio para o usuário
            const successEmoji = await getEmoji(interaction.guild.id, 'SUCCESS') || '✅';
            await interaction.reply({
                content: `${successEmoji} **Obrigado pela sua avaliação!**\nSua opinião foi enviada com sucesso.`,
                ephemeral: true
            });

            // Registra no log
            await logger.userAction('Avaliação enviada', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'review_submit'
            }, {
                cartId: cart._id.toString(),
                orderNumber: cart.orderNumber || cart._id,
                rating: rating,
                hasComment: comment.length > 0,
                total: cart.subtotal
            });

            // Finaliza compra com avaliação
            await this.finalizeWithReview(interaction.channel, cart);

        } catch (error) {
            await logger.error('Erro ao processar avaliação:', error);
            await interaction.reply({
                content: '❌ Erro ao processar sua avaliação. Tente novamente.',
                ephemeral: true
            });
        }
    }

    /**
     * Envia avaliação para o canal configurado
     * @param {Object} interaction - Interação do Discord
     * @param {Object} cart - Carrinho de compras
     * @param {number} rating - Nota da avaliação
     * @param {string} comment - Comentário da avaliação
     * @param {string} channelId - ID do canal de avaliações
     */
    static async sendReviewToChannel(interaction, cart, rating, comment, channelId) {
        try {
            const reviewsChannel = interaction.guild.channels.cache.get(channelId);
            if (!reviewsChannel) {
                throw new Error('Canal de avaliações não encontrado');
            }

            const starEmoji = await getEmoji(interaction.guild.id, 'STAR') || '⭐';
            const userEmoji = await getEmoji(interaction.guild.id, 'USER') || '👤';
            const packageEmoji = await getEmoji(interaction.guild.id, 'PACKAGE') || '📦';

            // Cria string de estrelas
            const stars = '⭐'.repeat(rating) + '☆'.repeat(5 - rating);

            const embed = new EmbedBuilder()
                .setColor(rating >= 4 ? COLORS.SUCCESS : rating >= 3 ? COLORS.WARNING : COLORS.ERROR)
                .setTitle(`${starEmoji} Nova Avaliação`)
                .setDescription(
                    `**Nota:** ${stars} (${rating}/5)\n\n` +
                    (comment ? `**Comentário:**\n"${comment}"\n\n` : '') +
                    `**Produtos:**\n` +
                    cart.items.map(item => 
                        `${packageEmoji} ${item.productName} (${item.quantity}x)`
                    ).join('\n')
                )
                .addFields(
                    {
                        name: `${userEmoji} Cliente`,
                        value: `${interaction.user}`,
                        inline: true
                    },
                    {
                        name: '💰 Valor Total',
                        value: `${BOT_CONFIG.STORE.CURRENCY_SYMBOL} ${cart.subtotal.toFixed(2)}`,
                        inline: true
                    },
                    {
                        name: '📅 Data',
                        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                        inline: true
                    }
                )
                .setFooter({
                    text: `Pedido: ${cart.orderNumber || cart._id} | Sistema de Avaliações`,
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            await reviewsChannel.send({ embeds: [embed] });

        } catch (error) {
            await logger.error('Erro ao enviar avaliação para canal:', error);
            throw error;
        }
    }

    /**
     * Finaliza compra com avaliação
     * @param {Object} channel - Canal do Discord
     * @param {Object} cart - Carrinho de compras
     */
    static async finalizeWithReview(channel, cart) {
        try {
            // Agenda limpeza do canal em 1 minuto
            setTimeout(async () => {
                try {
                    const ShoppingChannelManager = (await import('./shoppingChannelManager.js')).default;
                    await ShoppingChannelManager.cleanupShoppingChannel(
                        channel,
                        'Compra finalizada - com avaliação'
                    );
                } catch (error) {
                    await logger.error('Erro ao limpar canal após finalização com avaliação:', error);
                }
            }, 1 * 60 * 1000); // 1 minuto

        } catch (error) {
            await logger.error('Erro ao finalizar compra com avaliação:', error);
        }
    }
}

export default ReviewManager;
