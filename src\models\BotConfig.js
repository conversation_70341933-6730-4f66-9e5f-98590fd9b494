import mongoose from 'mongoose';

const botConfigSchema = new mongoose.Schema({
    // Identificação do servidor
    guildId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    
    // Canais de logs especializados
    logChannels: {
        // Canal para logs administrativos gerais
        admin: {
            type: String,
            default: null
        },
        // Canal para logs públicos (vendas, etc.)
        public: {
            type: String,
            default: null
        },
        // Canal para logs de erro e exceções
        error: {
            type: String,
            default: null
        },
        // Canal para logs de moderação e segurança
        moderation: {
            type: String,
            default: null
        },
        // Canal para logs do sistema (inicialização, conexões)
        system: {
            type: String,
            default: null
        },
        // Canal para logs de debug (desenvolvimento)
        debug: {
            type: String,
            default: null
        },
        // Canal para logs de comandos executados
        commands: {
            type: String,
            default: null
        },
        // Canal para logs de eventos do Discord
        events: {
            type: String,
            default: null
        },
        // Canal para logs de operações de banco de dados
        database: {
            type: String,
            default: null
        },
        // Canal para logs de APIs externas
        api: {
            type: String,
            default: null
        }
    },

    // Configurações de logging
    logSettings: {
        // Habilitar/desabilitar logs por tipo
        enabledTypes: {
            system: { type: Boolean, default: true },
            command: { type: Boolean, default: true },
            event: { type: Boolean, default: true },
            database: { type: Boolean, default: false },
            api: { type: Boolean, default: true },
            security: { type: Boolean, default: true },
            error: { type: Boolean, default: true },
            userAction: { type: Boolean, default: true },
            performance: { type: Boolean, default: false }
        },
        // Nível mínimo de log para Discord
        discordLogLevel: {
            type: String,
            enum: ['ERROR', 'WARN', 'INFO', 'DEBUG'],
            default: 'WARN'
        },
        // Incluir stack trace em logs de erro
        includeStackTrace: {
            type: Boolean,
            default: true
        }
    },

    // Manter compatibilidade com campos antigos
    adminLogChannelId: {
        type: String,
        default: null
    },
    publicLogChannelId: {
        type: String,
        default: null
    },
    
    // Configurações do MercadoPago
    mercadoPago: {
        accessToken: {
            type: String,
            default: null
        },
        publicKey: {
            type: String,
            default: null
        },
        webhookSecret: {
            type: String,
            default: null
        },
        isEnabled: {
            type: Boolean,
            default: false
        }
    },
    
    // Rate Limiting
    rateLimiting: {
        windowMs: {
            type: Number,
            default: 60000 // 1 minuto
        },
        maxRequests: {
            type: Number,
            default: 10
        },
        isEnabled: {
            type: Boolean,
            default: true
        }
    },

    // Canal de avaliações
    reviewsChannelId: {
        type: String,
        default: null
    },

    // Metadados
    lastModifiedBy: {
        type: String // Discord ID do admin que modificou
    }
}, {
    timestamps: true,
    collection: 'bot_configs'
});

// Métodos estáticos
botConfigSchema.statics.findByGuild = function(guildId) {
    return this.findOne({ guildId });
};

botConfigSchema.statics.createOrUpdate = async function(guildId, updates, modifiedBy) {
    const config = await this.findOneAndUpdate(
        { guildId },
        { 
            ...updates,
            lastModifiedBy: modifiedBy,
            updatedAt: new Date()
        },
        { 
            upsert: true, 
            new: true,
            setDefaultsOnInsert: true
        }
    );
    return config;
};

// Métodos de instância
botConfigSchema.methods.updateAdminLogChannel = function(channelId, modifiedBy) {
    this.adminLogChannelId = channelId;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updatePublicLogChannel = function(channelId, modifiedBy) {
    this.publicLogChannelId = channelId;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updateMercadoPago = function(mercadoPagoConfig, modifiedBy) {
    this.mercadoPago = { ...this.mercadoPago, ...mercadoPagoConfig };
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updateRateLimiting = function(rateLimitConfig, modifiedBy) {
    this.rateLimiting = { ...this.rateLimiting, ...rateLimitConfig };
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

botConfigSchema.methods.updateReviewsChannel = function(channelId, modifiedBy) {
    this.reviewsChannelId = channelId;
    this.lastModifiedBy = modifiedBy;
    return this.save();
};

export default mongoose.model('BotConfig', botConfigSchema);