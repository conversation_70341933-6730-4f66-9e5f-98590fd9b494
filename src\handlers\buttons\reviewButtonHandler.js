import { logger } from '../../utils/logging/logger.js';
import { ReviewManager } from '../../utils/managers/reviewManager.js';
import ShoppingCart from '../../models/ShoppingCart.js';

/**
 * Handler para botões relacionados às avaliações
 */
export class ReviewButtonHandler {
    /**
     * Manipula botões relacionados às avaliações
     * @param {Object} interaction - Interação do Discord
     */
    static async handleReviewButton(interaction) {
        try {
            const customId = interaction.customId;
            logger.info(`Botão de avaliação clicado: ${customId} por ${interaction.user.tag}`);

            if (customId.startsWith('review_send_')) {
                await this.handleSendReview(interaction);
            } else if (customId.startsWith('review_skip_')) {
                await this.handleSkipReview(interaction);
            } else {
                logger.warn(`Botão de avaliação não reconhecido: ${customId}`);
                await interaction.reply({
                    content: '❌ Ação não reconhecida.',
                    ephemeral: true
                });
            }

        } catch (error) {
            await logger.error('Erro no handler de botão de avaliação:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Erro ao processar ação. Tente novamente.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botão "Enviar Avaliação"
     * @param {Object} interaction - Interação do Discord
     */
    static async handleSendReview(interaction) {
        try {
            // Extrai ID do carrinho do customId
            const cartId = interaction.customId.replace('review_send_', '');
            
            // Busca o carrinho
            const cart = await ShoppingCart.findById(cartId);
            if (!cart) {
                return await interaction.reply({
                    content: '❌ Carrinho não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.reply({
                    content: '❌ Você não pode avaliar este pedido.',
                    ephemeral: true
                });
            }

            // Cria e apresenta modal de avaliação
            const modal = ReviewManager.createReviewModal(cartId);
            await interaction.showModal(modal);

            await logger.info(`Modal de avaliação apresentado para carrinho ${cartId}`);

        } catch (error) {
            await logger.error('Erro ao processar botão "Enviar Avaliação":', error);
            await interaction.reply({
                content: '❌ Erro ao abrir formulário de avaliação.',
                ephemeral: true
            });
        }
    }

    /**
     * Manipula botão "Não Avaliar"
     * @param {Object} interaction - Interação do Discord
     */
    static async handleSkipReview(interaction) {
        try {
            // Extrai ID do carrinho do customId
            const cartId = interaction.customId.replace('review_skip_', '');
            
            // Busca o carrinho
            const cart = await ShoppingCart.findById(cartId);
            if (!cart) {
                return await interaction.reply({
                    content: '❌ Carrinho não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se o usuário é o dono do carrinho
            if (cart.userId !== interaction.user.id) {
                return await interaction.reply({
                    content: '❌ Você não pode finalizar este pedido.',
                    ephemeral: true
                });
            }

            // Confirma que não quer avaliar
            await interaction.reply({
                content: '✅ **Compra finalizada!**\nObrigado pela sua compra. Esperamos vê-lo novamente em breve!',
                ephemeral: true
            });

            // Desabilita botões da mensagem original
            await this.disableReviewButtons(interaction);

            // Finaliza compra sem avaliação
            await ReviewManager.finalizeWithoutReview(
                interaction.channel, 
                cart, 
                'Cliente optou por não avaliar'
            );

            await logger.info(`Cliente optou por não avaliar carrinho ${cartId}`);

        } catch (error) {
            await logger.error('Erro ao processar botão "Não Avaliar":', error);
            await interaction.followUp({
                content: '❌ Erro ao finalizar compra.',
                ephemeral: true
            });
        }
    }

    /**
     * Desabilita botões de avaliação na mensagem original
     * @param {Object} interaction - Interação do Discord
     */
    static async disableReviewButtons(interaction) {
        try {
            const { ActionRowBuilder, ButtonBuilder, ButtonStyle, EmbedBuilder } = await import('discord.js');
            const { COLORS } = await import('../../config/constants.js');
            const { getEmoji } = await import('../../utils/ui/emojiHelper.js');

            // Cria botões desabilitados
            const disabledRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('review_send_disabled')
                        .setLabel('Enviar Avaliação')
                        .setEmoji('⭐')
                        .setStyle(ButtonStyle.Primary)
                        .setDisabled(true),
                    new ButtonBuilder()
                        .setCustomId('review_skip_disabled')
                        .setLabel('Não Avaliar')
                        .setEmoji('❌')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(true)
                );

            // Cria embed de finalização
            const successEmoji = await getEmoji(interaction.guild.id, 'SUCCESS') || '✅';
            const embed = new EmbedBuilder()
                .setColor(COLORS.SUCCESS)
                .setTitle(`${successEmoji} Compra Finalizada`)
                .setDescription(
                    'Obrigado pela sua compra!\n' +
                    'Sua sessão foi finalizada com sucesso.'
                )
                .setFooter({
                    text: 'Sistema de Avaliações - Finalizado',
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            // Atualiza mensagem original
            await interaction.message.edit({
                embeds: [embed],
                components: [disabledRow]
            });

        } catch (error) {
            await logger.error('Erro ao desabilitar botões de avaliação:', error);
        }
    }
}

export default ReviewButtonHandler;
