import { logger } from '../../utils/logging/logger.js';
import { EmbedBuilder } from 'discord.js';
import Product from '../../models/Product.js';
import Store from '../../models/Store.js';
import StockItem from '../../models/StockItem.js';
import { VALIDATION } from '../../config/constants.js';

/**
 * Handler para modais relacionados à loja
 */
export class StoreModalHandler {
    /**
     * Manipula modais relacionados à loja
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do modal
     */
    static async handleStoreModal(interaction, action) {
        try {
            logger.info(`Modal de loja enviado: ${action} por ${interaction.user.tag}`);

            switch (action) {
                case 'create':
                    await this.handleStoreCreateModal(interaction);
                    break;
                case 'feedback':
                    await this.handleFeedbackModal(interaction);
                    break;
                case 'support':
                    await this.handleSupportModal(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Ação de modal de loja não reconhecida.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro no handler de modal de loja:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de criação de loja
     * @param {Object} interaction - Interação do Discord
     */
    static async handleStoreCreateModal(interaction) {
        try {
            logger.info(`Modal de criação de loja enviado por ${interaction.user.tag}`);

            // Extrai os dados do modal
            const storeName = interaction.fields.getTextInputValue('store_name').trim();
            const storeDescription = interaction.fields.getTextInputValue('store_description')?.trim() || '';
            const storeBanner = interaction.fields.getTextInputValue('store_banner').trim();
            const storeColor = interaction.fields.getTextInputValue('store_color').trim();

            // Usa o canal atual onde o comando foi executado
            const channelId = interaction.channel.id;

            // Validações básicas
            if (!storeName || storeName.length < VALIDATION.STORE_NAME.MIN_LENGTH) {
                return await interaction.reply({
                    content: `❌ O nome da loja deve ter pelo menos ${VALIDATION.STORE_NAME.MIN_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            if (storeName.length > VALIDATION.STORE_NAME.MAX_LENGTH) {
                return await interaction.reply({
                    content: `❌ O nome da loja deve ter no máximo ${VALIDATION.STORE_NAME.MAX_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            if (storeDescription.length > VALIDATION.STORE_DESCRIPTION.MAX_LENGTH) {
                return await interaction.reply({
                    content: `❌ A descrição deve ter no máximo ${VALIDATION.STORE_DESCRIPTION.MAX_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            // Validações adicionais
            if (!storeBanner) {
                return await interaction.reply({
                    content: '❌ O banner da loja é obrigatório.',
                    ephemeral: true
                });
            }

            // Validação da URL do banner
            const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i;
            if (!urlPattern.test(storeBanner)) {
                return await interaction.reply({
                    content: '❌ O banner deve ser uma URL válida de imagem (jpg, jpeg, png, gif, webp).',
                    ephemeral: true
                });
            }

            if (!storeColor) {
                return await interaction.reply({
                    content: '❌ A cor da loja é obrigatória.',
                    ephemeral: true
                });
            }

            // Validação da cor
            const colorPattern = /^#[0-9A-F]{6}$/i;
            const colorNames = ['red', 'green', 'blue', 'yellow', 'purple', 'orange', 'pink', 'black', 'white', 'gray', 'grey'];
            if (!colorPattern.test(storeColor) && !colorNames.includes(storeColor.toLowerCase())) {
                return await interaction.reply({
                    content: '❌ A cor deve ser um código hexadecimal válido (#FFFFFF) ou um nome de cor (red, blue, green, etc.).',
                    ephemeral: true
                });
            }

            // Usa o canal atual
            const channel = interaction.channel;

            if (!channel || channel.type !== 0) { // 0 = GUILD_TEXT
                return await interaction.reply({
                    content: '❌ Este comando só pode ser usado em canais de texto.',
                    ephemeral: true
                });
            }

            // Verifica se o bot tem permissões no canal
            const botMember = interaction.guild.members.cache.get(interaction.client.user.id);
            const permissions = channel.permissionsFor(botMember);

            if (!permissions.has(['ViewChannel', 'SendMessages', 'EmbedLinks'])) {
                return await interaction.reply({
                    content: `❌ O bot não tem permissões suficientes no canal ${channel}. Necessário: Ver Canal, Enviar Mensagens e Inserir Links.`,
                    ephemeral: true
                });
            }

            // Verifica se já existe uma loja com o mesmo nome no servidor
            const existingStore = await Store.findOne({
                guildId: interaction.guild.id,
                name: { $regex: new RegExp(`^${storeName}$`, 'i') },
                isActive: true
            });

            if (existingStore) {
                return await interaction.reply({
                    content: `❌ Já existe uma loja com o nome "${storeName}" neste servidor.`,
                    ephemeral: true
                });
            }

            // Verifica se já existe uma loja no canal especificado
            const existingChannelStore = await Store.findOne({
                guildId: interaction.guild.id,
                channelId: channel.id,
                isActive: true
            });

            if (existingChannelStore) {
                return await interaction.reply({
                    content: `❌ Já existe uma loja configurada no canal ${channel}. Uma loja por canal.`,
                    ephemeral: true
                });
            }

            // Cria a nova loja
            const newStore = new Store({
                name: storeName,
                description: storeDescription,
                banner: storeBanner,
                color: storeColor,
                channelId: channel.id,
                guildId: interaction.guild.id,
                createdBy: interaction.user.id,
                isActive: true
            });

            await newStore.save();

            logger.info(`Nova loja criada: "${storeName}" no canal ${channel.name} por ${interaction.user.tag}`);

            // Envia mensagem de confirmação
            await interaction.reply({
                content: `✅ **Loja criada com sucesso!**\n\n` +
                        `🏪 **Nome:** ${storeName}\n` +
                        `📝 **Descrição:** ${storeDescription || 'Não informada'}\n` +
                        `📍 **Canal:** ${channel}\n` +
                        `👤 **Proprietário:** ${interaction.user}\n` +
                        `🆔 **ID da Loja:** \`${newStore._id}\`\n\n` +
                        `🎉 Sua loja está pronta! Use \`/criar-produto\` para adicionar produtos.`,
                ephemeral: true
            });

            // Envia mensagem de boas-vindas no canal da loja
            const welcomeEmbed = new EmbedBuilder()
                .setColor(newStore.getColorAsInt())
                .setTitle(`🏪 ${storeName}`)
                .setDescription(storeDescription || 'Bem-vindos à nossa loja!')
                .setImage(storeBanner)
                .addFields(
                    { name: '👤 Proprietário', value: `${interaction.user}`, inline: true },
                    { name: '📅 Criada em', value: new Date().toLocaleDateString('pt-BR'), inline: true },
                    { name: '🛍️ Status', value: 'Loja ativa e funcionando!', inline: true }
                )
                .setFooter({ text: `ID: ${newStore._id}` })
                .setTimestamp();

            await channel.send({
                content: `🎉 **Nova loja criada!** 🎉`,
                embeds: [welcomeEmbed]
            });

        } catch (error) {
            logger.error('Erro no handler de modal de criação de loja:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao criar a loja. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de feedback
     * @param {Object} interaction - Interação do Discord
     */
    static async handleFeedbackModal(interaction) {
        try {
            const feedback = interaction.fields.getTextInputValue('feedback_text');

            await interaction.reply({
                content: `📝 Obrigado pelo seu feedback: "${feedback}"`,
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de feedback:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar seu feedback. Verifique se o modal foi preenchido corretamente.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de suporte
     * @param {Object} interaction - Interação do Discord
     */
    static async handleSupportModal(interaction) {
        try {
            const issue = interaction.fields.getTextInputValue('issue_description');

            await interaction.reply({
                content: `🎫 Ticket de suporte criado! Descrição: "${issue}"`,
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de suporte:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar seu ticket de suporte. Verifique se o modal foi preenchido corretamente.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de edição de loja
     * @param {Object} interaction - Interação do Discord
     * @param {string} storeId - ID da loja
     */
    static async handleStoreEditModal(interaction, storeId) {
        try {
            logger.info(`Modal de edição de loja enviado: ${storeId} por ${interaction.user.tag}`);

            await interaction.reply({
                content: `✏️ Funcionalidade de edição de loja será implementada em breve! (ID: ${storeId})`,
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de edição de loja:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a edição da loja.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de criação de produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} storeId - ID da loja
     */
    static async handleProductCreateModal(interaction, storeId) {
        try {
            logger.info(`Modal de criação de produto enviado: ${storeId} por ${interaction.user.tag}`);

            // Extrai os dados do modal
            const productName = interaction.fields.getTextInputValue('product_name').trim();
            const productPriceStr = interaction.fields.getTextInputValue('product_price').trim();
            const productEmoji = interaction.fields.getTextInputValue('product_emoji')?.trim() || null;

            // Validações básicas
            if (!productName || productName.length < VALIDATION.PRODUCT_NAME.MIN_LENGTH) {
                return await interaction.reply({
                    content: `❌ O nome do produto deve ter pelo menos ${VALIDATION.PRODUCT_NAME.MIN_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            if (productName.length > VALIDATION.PRODUCT_NAME.MAX_LENGTH) {
                return await interaction.reply({
                    content: `❌ O nome do produto deve ter no máximo ${VALIDATION.PRODUCT_NAME.MAX_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            // Validação e conversão do preço
            const productPrice = parseFloat(productPriceStr.replace(',', '.'));
            if (isNaN(productPrice) || productPrice < VALIDATION.PRODUCT_PRICE.MIN) {
                return await interaction.reply({
                    content: `❌ Preço inválido. O valor deve ser um número maior que R$ ${VALIDATION.PRODUCT_PRICE.MIN.toFixed(2)}.`,
                    ephemeral: true
                });
            }

            if (productPrice > VALIDATION.PRODUCT_PRICE.MAX) {
                return await interaction.reply({
                    content: `❌ Preço muito alto. O valor máximo é R$ ${VALIDATION.PRODUCT_PRICE.MAX.toFixed(2)}.`,
                    ephemeral: true
                });
            }

            // Verifica se a loja existe e está ativa
            const store = await Store.findById(storeId);
            if (!store || !store.isActive) {
                return await interaction.reply({
                    content: '❌ Loja não encontrada ou inativa.',
                    ephemeral: true
                });
            }

            // Verifica se a loja pertence ao servidor atual
            if (store.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Esta loja não pertence a este servidor.',
                    ephemeral: true
                });
            }

            // Verifica se já existe um produto com o mesmo nome na loja
            const existingProduct = await Product.findOne({
                storeId: storeId,
                name: { $regex: new RegExp(`^${productName}$`, 'i') },
                status: { $ne: 'discontinued' }
            });

            if (existingProduct) {
                return await interaction.reply({
                    content: `❌ Já existe um produto com o nome "${productName}" nesta loja.`,
                    ephemeral: true
                });
            }

            // Cria o novo produto
            const newProduct = new Product({
                name: productName,
                description: `Produto criado via Discord por ${interaction.user.tag}`,
                price: productPrice,
                stock: 0, // Estoque inicial zero
                category: 'digital', // Categoria padrão
                status: 'out_of_stock', // Sem estoque inicialmente
                storeId: storeId,
                emoji: productEmoji,
                createdBy: interaction.user.id
            });

            await newProduct.save();

            logger.info(`Produto "${productName}" criado com sucesso na loja "${store.name}" por ${interaction.user.tag}`);

            await interaction.reply({
                content: `✅ **Produto criado com sucesso!**\n\n` +
                        `📦 **Nome:** ${productName}\n` +
                        `💰 **Preço:** R$ ${productPrice.toFixed(2)}\n` +
                        `🏪 **Loja:** ${store.name}\n` +
                        `${productEmoji ? `${productEmoji} **Emoji:** ${productEmoji}\n` : ''}` +
                        `\n💡 **Próximo passo:** Use \`/criar-estoque\` para adicionar itens ao estoque do produto.`,
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro no handler de modal de criação de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao criar o produto. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de criação de estoque
     * @param {Object} interaction - Interação do Discord
     * @param {string} storeId - ID da loja
     * @param {string} productId - ID do produto
     */
    static async handleStockCreateModal(interaction, storeId, productId) {
        try {
            logger.info(`Modal de criação de estoque enviado: ${storeId}/${productId} por ${interaction.user.tag}`);

            // Extrai os dados do modal
            const stockLines = interaction.fields.getTextInputValue('stock_lines').trim();
            const stockNotes = interaction.fields.getTextInputValue('stock_notes')?.trim() || '';

            // Validações básicas
            if (!stockLines) {
                return await interaction.reply({
                    content: '❌ É necessário fornecer pelo menos uma linha de estoque.',
                    ephemeral: true
                });
            }

            // Verifica se o produto e a loja existem
            const [product, store] = await Promise.all([
                Product.findById(productId),
                Store.findById(storeId)
            ]);

            if (!product || !store) {
                return await interaction.reply({
                    content: '❌ Produto ou loja não encontrados.',
                    ephemeral: true
                });
            }

            if (!store.isActive) {
                return await interaction.reply({
                    content: '❌ A loja está inativa.',
                    ephemeral: true
                });
            }

            if (store.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Esta loja não pertence a este servidor.',
                    ephemeral: true
                });
            }

            if (product.storeId.toString() !== storeId) {
                return await interaction.reply({
                    content: '❌ Este produto não pertence à loja selecionada.',
                    ephemeral: true
                });
            }

            // Processa as linhas de estoque
            const lines = stockLines.split('\n').filter(line => line.trim());

            if (lines.length === 0) {
                return await interaction.reply({
                    content: '❌ Nenhuma linha de estoque válida encontrada.',
                    ephemeral: true
                });
            }

            // Valida o comprimento total e por linha
            if (stockLines.length > VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH) {
                return await interaction.reply({
                    content: `❌ O conteúdo do estoque é muito longo. Máximo: ${VALIDATION.STOCK_CONTENT.MAX_TOTAL_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            const invalidLines = lines.filter(line => line.length > VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH);
            if (invalidLines.length > 0) {
                return await interaction.reply({
                    content: `❌ Algumas linhas são muito longas. Máximo por linha: ${VALIDATION.STOCK_CONTENT.MAX_LINE_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            // Cria os itens de estoque
            const stockItems = [];
            for (const line of lines) {
                const stockItem = new StockItem({
                    productId: productId,
                    storeId: storeId,
                    content: line.trim(),
                    status: 'available',
                    createdBy: interaction.user.id,
                    notes: stockNotes
                });
                stockItems.push(stockItem);
            }

            // Salva todos os itens de estoque
            await StockItem.insertMany(stockItems);

            // Atualiza o status do produto se estava sem estoque
            if (product.status === 'out_of_stock') {
                product.status = 'active';
                await product.save();
            }

            logger.info(`${stockItems.length} itens de estoque adicionados ao produto "${product.name}" por ${interaction.user.tag}`);

            await interaction.reply({
                content: `✅ **Estoque adicionado com sucesso!**\n\n` +
                        `📦 **Produto:** ${product.name}\n` +
                        `📊 **Itens adicionados:** ${stockItems.length}\n` +
                        `🏪 **Loja:** ${store.name}\n` +
                        `${stockNotes ? `📝 **Observações:** ${stockNotes}\n` : ''}` +
                        `\n💡 O produto agora está disponível para venda!`,
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro no handler de modal de criação de estoque:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao adicionar o estoque. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de edição de item de estoque
     * @param {Object} interaction - Interação do Discord
     * @param {string} stockItemId - ID do item de estoque
     */
    static async handleEditStockItemModal(interaction, stockItemId) {
        try {
            logger.info(`Modal de edição de item de estoque enviado: ${stockItemId} por ${interaction.user.tag}`);

            // Extrai os dados do modal
            const content = interaction.fields.getTextInputValue('content').trim();
            const notes = interaction.fields.getTextInputValue('notes')?.trim() || '';

            // Validações básicas
            if (!content) {
                return await interaction.reply({
                    content: '❌ O conteúdo do item é obrigatório.',
                    ephemeral: true
                });
            }

            // Busca o item de estoque
            const stockItem = await StockItem.findById(stockItemId);
            if (!stockItem) {
                return await interaction.reply({
                    content: '❌ Item de estoque não encontrado.',
                    ephemeral: true
                });
            }

            // Atualiza o item
            stockItem.content = content;
            stockItem.notes = notes;
            stockItem.lastModifiedBy = interaction.user.id;
            await stockItem.save();

            logger.info(`Item de estoque ${stockItemId} editado por ${interaction.user.tag}`);

            await interaction.reply({
                content: `✅ **Item de estoque atualizado com sucesso!**\n\n` +
                        `📦 **Conteúdo:** ${content}\n` +
                        `${notes ? `📝 **Observações:** ${notes}\n` : ''}` +
                        `🔄 **Atualizado por:** ${interaction.user}`,
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro no handler de modal de edição de item de estoque:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a edição do item de estoque.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de busca de estoque
     * @param {Object} interaction - Interação do Discord
     * @param {string} productId - ID do produto
     */
    static async handleStockSearchModal(interaction, productId) {
        try {
            logger.info(`Modal de busca de estoque enviado: ${productId} por ${interaction.user.tag}`);

            await interaction.reply({
                content: `🔍 Funcionalidade de busca de estoque será implementada em breve! (Produto: ${productId})`,
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de modal de busca de estoque:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a busca de estoque.',
                    ephemeral: true
                });
            }
        }
    }
}
